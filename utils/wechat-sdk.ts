// 微信公众号JS-SDK配置
import http from './request'

interface WechatConfig {
  appId: string
  timestamp: string
  nonceStr: string
  signature: string
  jsApiList: string[]
}

class WechatSDK {
  private _isReady = false
  private config: WechatConfig | null = null

  // 获取是否已初始化
  get isReady(): boolean {
    return this._isReady
  }

  // 初始化微信JS-SDK
  async init(config: WechatConfig): Promise<boolean> {
    try {
      this.config = config

      // 动态加载微信JS-SDK
      await this.loadWechatSDK()

      // 配置微信JS-SDK
      await this.configWechatSDK(config)

      this._isReady = true
      return true
    } catch (error) {
      console.error('微信JS-SDK初始化失败:', error)
      return false
    }
  }

  // 动态加载微信JS-SDK
  private loadWechatSDK(): Promise<void> {
    return new Promise((resolve, reject) => {
      // 检查是否已经加载
      if (window.wx && typeof window.wx.config === 'function') {
        resolve()
        return
      }

      // 如果正在加载中，等待加载完成
      if ((window as any).__wechat_sdk_loading__) {
        const checkInterval = setInterval(() => {
          if (window.wx && typeof window.wx.config === 'function') {
            clearInterval(checkInterval)
            resolve()
          }
        }, 100)

        // 设置超时
        setTimeout(() => {
          clearInterval(checkInterval)
          reject(new Error('微信JS-SDK加载超时'))
        }, 10000)
        return
      }

      // 标记正在加载
      (window as any).__wechat_sdk_loading__ = true

      const script = document.createElement('script')
      script.src = 'https://res.wx.qq.com/open/js/jweixin-1.6.0.js'

      script.onload = () => {
        // 等待wx对象初始化完成
        const checkWxReady = () => {
          if (window.wx && typeof window.wx.config === 'function') {
            (window as any).__wechat_sdk_loading__ = false
            resolve()
          } else {
            setTimeout(checkWxReady, 50)
          }
        }

        // 开始检查wx对象
        setTimeout(checkWxReady, 100)

        // 设置超时
        setTimeout(() => {
          (window as any).__wechat_sdk_loading__ = false
          reject(new Error('微信JS-SDK初始化超时'))
        }, 10000)
      }

      script.onerror = () => {
        (window as any).__wechat_sdk_loading__ = false
        reject(new Error('微信JS-SDK加载失败'))
      }

      document.head.appendChild(script)
    })
  }

  // 配置微信JS-SDK
  private configWechatSDK(config: WechatConfig): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!window.wx || typeof window.wx.config !== 'function') {
        reject(new Error('微信JS-SDK未正确加载'))
        return
      }

      window.wx.config({
        debug: false,
        appId: config.appId,
        timestamp: config.timestamp,
        nonceStr: config.nonceStr,
        signature: config.signature,
        jsApiList: config.jsApiList
      })

      window.wx.ready(() => {
        console.log('微信JS-SDK配置成功')
        resolve()
      })

      window.wx.error((res: any) => {
        console.error('微信JS-SDK配置失败:', res)
        reject(new Error(res.errMsg))
      })
    })
  }

  // 分享到微信好友
  shareToFriend(shareData: {
    title: string
    desc: string
    link: string
    imgUrl: string
  }): Promise<void> {

    return new Promise((resolve, reject) => {
      if (!this._isReady || !WechatSDK.isWechatSDKAvailable()) {
        reject(new Error('微信JS-SDK未初始化'))
        return
      }

      window.wx.updateAppMessageShareData({
        title: shareData.title,
        desc: shareData.desc,
        link: shareData.link,
        imgUrl: shareData.imgUrl,
        success: () => {
          console.log('分享到微信好友配置成功')
          resolve()
        },
        fail: (res: any) => {
          console.error('分享到微信好友配置失败:', res)
          reject(new Error(res.errMsg))
        }
      })
    })
  }

  // 分享到朋友圈
  shareToTimeline(shareData: {
    title: string
    link: string
    imgUrl: string
  }): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this._isReady || !WechatSDK.isWechatSDKAvailable()) {
        reject(new Error('微信JS-SDK未初始化'))
        return
      }

      window.wx.updateTimelineShareData({
        title: shareData.title,
        link: shareData.link,
        imgUrl: shareData.imgUrl,
        success: () => {
          console.log('分享到朋友圈配置成功')
          resolve()
        },
        fail: (res: any) => {
          console.error('分享到朋友圈配置失败:', res)
          reject(new Error(res.errMsg))
        }
      })
    })
  }

  // 检查是否在微信浏览器中
  static isWechatBrowser(): boolean {
    return /micromessenger/i.test(navigator.userAgent)
  }

  // 检查WeChat SDK是否可用
  static isWechatSDKAvailable(): boolean {
    return !!(window.wx && typeof window.wx.config === 'function')
  }

  // 获取微信分享配置（需要后端提供）
  static async getWechatConfig(): Promise<WechatConfig> {
    try {
      // 这里需要调用后端接口获取微信JS-SDK配置
      // 后端需要根据当前URL生成签名
      const response  = await http.get('/api.app/jssdk', {
        url: window.location.href.split('#')[0] // 微信分享需要当前页面URL
      })

      // 确保返回的数据符合WechatConfig接口
      const configData = response as any
      if (configData && configData.code && configData.data) {
        return configData.data as WechatConfig
      } else if (configData && configData.appId) {
        // 如果后端直接返回配置数据
        return configData as WechatConfig
      } else {
        throw new Error('微信配置数据格式错误')
      }
    } catch (error) {
      console.error('获取微信配置失败:', error)
      throw error
    }
  }
}

// 扩展window对象类型
declare global {
  interface Window {
    wx: any
  }
}

export default WechatSDK
