interface RequestOptions {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  data?: any
  header?: any
}

const baseURL = import.meta.env.VITE_APP_API_BASE_URL

// 创建请求函数
const request = (options: RequestOptions) => {
  return new Promise((resolve, reject) => {
    const token = uni.getStorageSync('token')
    
    uni.request({
      url: baseURL + options.url,
      method: options.method || 'GET',
      data: options.data,
      header: {
        'Content-Type': 'application/json',
        'token': token || '',
        ...options.header
      },
      success: (res: any) => {
        if (res.statusCode === 200) {
          resolve(res.data)
        } else if (res.statusCode === 401) {
          // token 过期或无效
          uni.removeStorageSync('token')
          // TODO: 跳转到登录页面
          reject(new Error('未授权，请重新登录'))
        } else {
          reject(new Error(res.data.message || '请求失败'))
        }
      },
      fail: (err) => {
        reject(new Error(err.errMsg || '网络错误'))
      }
    })
  })
}

// 封装常用请求方法
export const http = {
  get: (url: string, data?: any) => {
    return request({
      url,
      method: 'GET',
      data
    })
  },
  post: (url: string, data?: any) => {
    return request({
      url,
      method: 'POST',
      data
    })
  },
  put: (url: string, data?: any) => {
    return request({
      url,
      method: 'PUT',
      data
    })
  },
  delete: (url: string, data?: any) => {
    return request({
      url,
      method: 'DELETE',
      data
    })
  }
}

export default http 