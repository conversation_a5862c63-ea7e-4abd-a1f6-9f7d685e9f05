<template>
  <view class="container">
    <view class="nav">
      <swiper class="swiper" circular autoplay interval="3000" duration="500" indicator-dots
              indicator-active-color="#ffffff">
        <swiper-item v-for="(image, index) in voteSetting.images" :key="index">
          <image :src="image" mode="aspectFill" lazy-load/>
        </swiper-item>
      </swiper>
    </view>
    <view class="rules">
      <view class="title">{{ voteSetting.title }}</view>
      <view class="content">
        {{ voteSetting.content }}
      </view>
    </view>
    <vote-options ref="voteOptionsRef" :id="voteSetting.id" v-if="voteSetting.id"/>
    <view class="share">
      <!-- #ifdef MP-WEIXIN -->
      <button class="default-btn" open-type="share">分享给好友</button>
      <!-- #endif -->
      <!-- #ifdef WEB -->
      <button class="default-btn" @click="showSharePanel">分享给好友</button>
      <!-- #endif -->
      <button class="default-btn" @click="showResults">查看排名</button>
    </view>

    <!-- 排名弹窗 -->
    <vote-result v-model="resultsShow" :list="voteOptionsRef?.list || []"/>
  </view>
</template>

<script setup lang="ts">
import {ref, computed, nextTick} from 'vue'
import {onShareAppMessage, onShareTimeline, onLoad} from '@dcloudio/uni-app'
import VoteResult from '@/components/vote-result/vote-result.vue'
import VoteOptions from '@/components/vote-options/vote-options.vue'
import WechatSDK from '@/utils/wechat-sdk'
import {useVote} from './useVote'
import {useShare} from './useShare'

const resultsShow = ref(false)
const voteOptionsRef = ref()
const {
  voteSetting,
  getConfig
} = useVote()

// 使用 computed 创建响应式分享信息
const shareInfoComputed = computed(() => {
  let path = ''
  // #ifdef MP-WEIXIN
  path = '/pages/index/index?id=' + voteSetting.value?.id
  // #endif
  // #ifdef WEB
  // 删除window.location.href中的code参数，保留其他参数
  const url = window.location.href
  const urlObj = new URL(url)
  urlObj.searchParams.delete('code')
  urlObj.searchParams.delete('state')
  path = urlObj.toString()
  // #endif

  return {
    title: voteSetting.value?.share_title || voteSetting.value?.title || '投票活动',
    desc: voteSetting.value?.share_desc || voteSetting.value?.content || '参与投票活动',
    imageUrl: voteSetting.value?.share_image || voteSetting.value?.images?.[0] || '',
    path: path
  }
})

const {initShare} = useShare()

// 显示分享面板
const showSharePanel = () => {
  // #ifdef WEB
  if (WechatSDK.isWechatBrowser()) {
    // 微信浏览器H5环境：显示引导提示
    uni.showModal({
      title: '分享提示',
      content: '请点击右上角...进行分享',
      showCancel: false,
      confirmText: '知道了'
    })
  } else {
    // 非微信浏览器H5环境：调用navigator.share
    if (navigator.share) {
      const shareInfo = shareInfoComputed.value
      navigator.share({
        title: shareInfo.title,
        text: shareInfo.desc,
        url: window.location.href
      }).catch(err => {
        console.log('分享失败:', err)
        // 降级方案：复制链接
        copyToClipboard(window.location.href)
      })
    } else {
      // 不支持navigator.share，降级到复制链接
      copyToClipboard(window.location.href)
    }
  }
  // #endif
}

// 复制到剪贴板（降级方案）
const copyToClipboard = (text: string) => {
  // #ifdef WEB
  if (navigator.clipboard) {
    navigator.clipboard.writeText(text).then(() => {
      uni.showToast({
        title: '链接已复制',
        icon: 'success'
      })
    }).catch(() => {
      copyToClipboardFallback(text)
    })
  } else {
    copyToClipboardFallback(text)
  }
  // #endif
}

// 复制到剪贴板（兼容方案）
const copyToClipboardFallback = (text: string) => {
  // #ifdef WEB
  const textArea = document.createElement('textarea')
  textArea.value = text
  document.body.appendChild(textArea)
  textArea.select()
  try {
    document.execCommand('copy')
    uni.showToast({
      title: '链接已复制',
      icon: 'success'
    })
  } catch (err) {
    uni.showToast({
      title: '复制失败',
      icon: 'error'
    })
  }
  document.body.removeChild(textArea)
  // #endif
}

// 显示结果
const showResults = () => {
  resultsShow.value = true
}

const init = async (options: any) => {
  if (options?.id) {
    await getConfig(options?.id)
    // #ifdef WEB
    // 初始化微信公众号JS-SDK
    nextTick(() => {
      initShare(shareInfoComputed.value)
    })
    // #endif
  }
}

onLoad(async (options) => {
  init(options)
})

// #ifdef MP-WEIXIN
onShareAppMessage(() => {
  const shareInfo = shareInfoComputed.value
  return {
    title: shareInfo.title,
    desc: shareInfo.desc,
    imageUrl: shareInfo.imageUrl,
    path: shareInfo.path
  }
})
// 小程序分享到朋友圈
onShareTimeline(() => {
  const shareInfo = shareInfoComputed.value
  return {
    title: shareInfo.title,
    imageUrl: shareInfo.imageUrl,
    query: 'id=' + voteSetting.value?.id
  }
})

// #endif
</script>

<style lang="scss">
.container {
  padding: $uni-spacing-row-base;
  padding-bottom: 160rpx;

  .nav {
    margin-bottom: $uni-spacing-row-base;

    .swiper {
      width: 100%;
      height: 300rpx;
      border-radius: $uni-border-radius-base;
      overflow: hidden;
    }

    image {
      width: 100%;
      height: 300rpx;
    }
  }

  .rules {
    margin-bottom: $uni-spacing-row-base;

    .title {
      font-size: $uni-font-size-lg;
      font-weight: bold;
      margin-bottom: $uni-spacing-col-sm;
    }

    .content {
      font-size: $uni-font-size-base;
      color: $uni-text-color-grey;
    }
  }

  .list {
    display: flex;
    flex-wrap: wrap;
    margin: 0 $uni-spacing-row-sm;

    .list-item {
      margin: $uni-spacing-row-sm;
      background: $uni-bg-color;
      border-radius: $uni-border-radius-base;
      overflow: hidden;
      @include box-shadow(1);

      .image_box {
        position: relative;
        overflow: hidden;
        height: 300rpx;

        .votes {
          position: absolute;
          top: $uni-spacing-col-sm;
          right: $uni-spacing-col-sm;
          background: rgba(255, 255, 255, 0.8);
          color: $uni-color-primary;
          padding: 2rpx 8rpx;
          border-radius: 20rpx;
          font-size: $uni-font-size-sm;
          font-weight: 500;
          min-width: 60rpx;
          text-align: center;
          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
          letter-spacing: 1rpx;
          z-index: 1;
        }

        .item-image {
          width: 100%;
          height: 100%;
        }
      }

      .main-title {
        font-size: $uni-font-size-base;
        font-weight: bold;
        padding: $uni-spacing-col-sm;
      }

      .content {
        padding: 0 $uni-spacing-col-sm;

        .desc {
          font-size: $uni-font-size-sm;
          color: $uni-text-color-grey;
          @include multi-ellipsis(2);
        }
      }

      .vote-btn {
        padding: $uni-spacing-row-sm;

        .primary-btn {
          width: 100%;
          background-color: rgba($uni-color-primary, 0.9);
          color: $uni-text-color-inverse;
          font-size: $uni-font-size-base;
          padding: 4rpx 0;
          border-radius: $uni-border-radius-base;
          transition: all 0.3s ease;
          border: none;

          &:active {
            background-color: rgba($uni-color-primary, 0.8);
            transform: scale(0.98);
          }

          &[disabled] {
            background-color: rgba($uni-text-color-disable, 0.5);
          }
        }
      }
    }
  }

  .share {
    @include flex-between;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: $uni-bg-color;
    padding: $uni-spacing-row-base;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
    z-index: 100;

    .default-btn {
      width: 45%;
      color: $uni-color-primary;
      border: none;
      background-color: rgba($uni-color-primary, 0.08);
      font-size: $uni-font-size-base;
      padding: 4rpx 0;
      border-radius: $uni-border-radius-base;
      transition: all 0.3s ease;

      &:active {
        background-color: rgba($uni-color-primary, 0.15);
        transform: scale(0.98);
      }
    }
  }
}
</style>
