import { ref, Ref, watch } from 'vue'
import WechatSDK from '@/utils/wechat-sdk'

// 通用分享参数接口
export interface ShareInfo {
  title?: string
  desc?: string
  imageUrl?: string
  path?: string
  [key: string]: any // 允许扩展
}

export function useShare() {
  const wechatSDK = ref<WechatSDK | null>(null)

  // 初始化微信公众号JS-SDK
  const initWechatSDK = async () => {
    // #ifdef WEB
    if (WechatSDK.isWechatBrowser()) {
      try {
        const config = await WechatSDK.getWechatConfig()
        wechatSDK.value = new WechatSDK()
        const success = await wechatSDK.value.init(config)
        if (success) {
          console.log('微信公众号JS-SDK初始化成功')
        } else {
          console.warn('微信公众号JS-SDK初始化失败，将使用降级分享方案')
        }
      } catch (error) {
        console.error('微信公众号JS-SDK初始化失败:', error)
        // 不抛出错误，让应用继续运行，使用降级分享方案
      }
    }
    // #endif
  }

  const setShareInfo = (source: ShareInfo) => {
    // #ifdef WEB
    if (wechatSDK.value && WechatSDK.isWechatBrowser()) {
      wechatSDK.value.shareToFriend({
        title: source.title || '',
        desc: source.desc || '',
        link: source.path || '',
        imgUrl: source.imageUrl || ''
      })
      wechatSDK.value.shareToTimeline({
        title: source.title || '',
        link: source.path || '',
        imgUrl: source.imageUrl || ''
      })
    }
    // #endif
  }

  const initShare = async (shareInfo: ShareInfo) => {
    await initWechatSDK()
    setShareInfo(shareInfo)
  }




  return {
    initShare
  }
}
