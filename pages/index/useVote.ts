import {ref} from 'vue'
import http from '@/utils/request'

// 定义接口类型
interface VoteSetting {
  id: number
  title: string
  subtitle: string
  images: string
  content: string
  start_time: string
  end_time: string
  share_title: string
  share_image: string
  share_desc: string
}

export function useVote() {
  const voteSetting = ref<VoteSetting>({
    id: 0,
    title: '',
    subtitle: '',
    images: '',
    content: '',
    start_time: '',
    end_time: '',
    share_title: '',
    share_image: '',
    share_desc: ''
  })

  const getTokenByAntiSpam = async () => {
    if (uni.getStorageSync('token')) return

    // #ifdef MP-WEIXIN
    await new Promise((resolve) => {
      uni.login({
        success: async (loginRes) => {
          if (loginRes.code) {
            try {
              const res: any = await http.post('/api.auth/wx_login', {code: loginRes.code})
              if (res.code === 1 && res.data.token) {
                uni.setStorageSync('token', res.data.token)
              }
            } catch (e) {
              console.error('wx_login失败', e)
            }
          }
          resolve(true)
        },
        fail: () => resolve(false)
      })
    })
    // #endif
    // #ifdef WEB
    try {
      function isWeixinBrowser() {
        return /micromessenger/i.test(window.navigator.userAgent)
      }

      function getQueryParam(name: string): string | null {
        const match = window.location.search.match(new RegExp('[?&]' + name + '=([^&]*)'))
        return match ? decodeURIComponent(match[1]) : null
      }

      async function redirectToWeixinOAuthByApi(redirectUri: string) {
        try {
          const res: any = await http.get('/api.auth/get_auth_url', {redirect_uri: redirectUri})
          if (res.code === 1 && res.data.url) {
            window.location.replace(res.data.url)
          } else {
            throw new Error('获取微信授权url失败' + res.msg || '')
          }
        } catch (e) {
          throw new Error('请求微信授权url接口异常' + e || '')
        }
      }

      if (isWeixinBrowser()) {
        const code = getQueryParam('code')
        if (!code) {
          await redirectToWeixinOAuthByApi(window.location.href)
          return
        } else {
          const res: any = await http.get('/api.auth/h5_login', {code})
          if (res.code === 1 && res.data.token) {
            uni.setStorageSync('token', res.data.token)
          }
        }
      } else {
        throw new Error('请在微信内打开')
      }
    } catch (e) {
      uni.showModal({
        title: '错误',
        content: (e as Error).message,
        showCancel: false,
        confirmText: '知道了'
      })
    }
    // #endif

  }

  const getConfig = async (id?: string) => {
    try {
      // 如果有id，带id请求，否则默认
      const url = id ? `/api.app/config?id=${id}` : '/api.app/config'
      const res: any = await http.get(url)
      if (res.code === 1) {
        voteSetting.value = res.data.setting
        uni.setNavigationBarTitle({title: res.data.setting.title})
        await getTokenByAntiSpam()
      }
    } catch (error) {
      console.error('获取配置失败:', error)
    }
  }

  return {
    voteSetting,
    getConfig
  }
}
