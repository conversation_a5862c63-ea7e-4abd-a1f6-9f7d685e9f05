<template>
  <view class="detail-container">
    <!-- 头部图片 -->
    <view class="header-image">
      <image :src="voteItem.image" mode="aspectFill" class="detail-image" />
      <view class="votes-badge" v-if="voteItem.votes !== undefined && voteItem.votes !== null">
        {{ formatVotes(voteItem.votes) }}
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="content-section">
      <!-- 标题和描述 -->
      <view class="basic-info">
        <view class="title">{{ voteItem.name }}</view>
        <view class="description" v-if="voteItem.description">{{ voteItem.description }}</view>
      </view>

      <!-- 富文本内容 -->
      <view class="rich-content" v-if="voteItem.content">
        <view class="section-title">详细介绍</view>
        <rich-text :nodes="voteItem.content" class="rich-text-content"></rich-text>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="action-buttons">
      <button class="vote-btn" :class="{ 'btn-disabled': disable }" @click="vote" :disabled="disable">
        投 票
      </button>
      <button class="rank-btn" @click="showResults">
        查看排名
      </button>
    </view>

    <!-- 排名弹窗 -->
    <vote-result v-model="resultsShow" :list="allVoteItems" />
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import VoteResult from '@/components/vote-result/vote-result.vue'
import http from '@/utils/request'

interface VoteItem {
  id: number
  name: string
  description: string
  image: string
  votes: number
  content?: string
}

const voteItem = ref<VoteItem>({
  id: 0,
  name: '',
  description: '',
  image: '',
  votes: 0,
  content: ''
})

const allVoteItems = ref<VoteItem[]>([])
const resultsShow = ref(false)
const disable = ref(false)
const voteId = ref(0)

const formatVotes = (votes: number) => {
  if (votes >= 10000) {
    return `${(votes / 10000).toFixed(1)}w`
  } else if (votes >= 1000) {
    return `${(votes / 1000).toFixed(1)}k`
  }
  return votes.toString()
}

// 获取投票项详情
const getVoteItemDetail = async (id: number, vote_id: number) => {
  try {
    // 先尝试获取详情接口，如果不存在则从选项列表中查找
    let detailRes: any
    try {
      detailRes = await http.get('/api.vote/detail', { id })
    } catch (detailError) {
      // 如果详情接口不存在，从选项列表中获取
      const optionsRes: any = await http.get('/api.vote/options', { vote_id })
      if (optionsRes.code === 1) {
        const item = optionsRes.data.list.find((item: VoteItem) => item.id === id)
        if (item) {
          voteItem.value = item
          return
        }
      }
      throw detailError
    }

    if (detailRes.code === 1) {
      voteItem.value = detailRes.data
    }
  } catch (error) {
    console.error('获取详情失败:', error)
    uni.showToast({ title: '获取详情失败', icon: 'none' })
  }
}

// 获取所有投票选项（用于排名显示）
const getAllVoteOptions = async (vote_id: number) => {
  try {
    const res: any = await http.get('/api.vote/options', { vote_id })
    if (res.code === 1) {
      allVoteItems.value = res.data.list
    }
  } catch (error) {
    console.error('获取选项失败:', error)
  }
}

// 投票功能
const vote = async () => {
  if (disable.value) return
  disable.value = true

  const token = uni.getStorageSync('token')
  if (!token) {
    uni.showToast({ title: '请先登录', icon: 'none' })
    disable.value = false
    return
  }

  try {
    const res: any = await http.post('/api.vote/vote', {
      id: voteItem.value.id,
      vote_id: voteId.value
    })
    if (res.code === 1) {
      voteItem.value = { ...voteItem.value, votes: res.data.votes }
      // 更新所有选项列表中的对应项
      const index = allVoteItems.value.findIndex(item => item.id === res.data.id)
      if (index !== -1) {
        allVoteItems.value[index] = res.data
      }
      uni.showToast({ title: '投票成功', icon: 'success' })
    } else {
      uni.showToast({ title: res.msg || '投票失败', icon: 'none' })
    }
  } catch (error) {
    uni.showToast({ title: '系统繁忙，请重试', icon: 'none' })
  } finally {
    disable.value = false
  }
}

// 显示排名
const showResults = () => {
  resultsShow.value = true
}

onLoad(async (options) => {
  if (options?.id && options?.vote_id) {
    voteId.value = parseInt(options.vote_id)
    await getVoteItemDetail(parseInt(options.id), voteId.value)
    await getAllVoteOptions(voteId.value)
  } else {
    uni.showToast({ title: '参数错误', icon: 'none' })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
})
</script>

<style lang="scss" scoped>
.detail-container {
  min-height: 100vh;
  background-color: $uni-bg-color-grey;
  padding-bottom: 120rpx;
}

.header-image {
  position: relative;
  width: 100%;
  height: 500rpx;

  .detail-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .votes-badge {
    position: absolute;
    top: 30rpx;
    right: 30rpx;
    background: rgba(255, 255, 255, 0.9);
    color: $uni-color-primary;
    padding: 8rpx 16rpx;
    border-radius: 30rpx;
    font-size: $uni-font-size-base;
    font-weight: 600;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10rpx);
  }
}

.content-section {
  background: $uni-bg-color;
  margin: 20rpx;
  border-radius: $uni-border-radius-lg;
  overflow: hidden;
  @include box-shadow(1);
}

.basic-info {
  padding: 40rpx 30rpx 30rpx;

  .title {
    font-size: $uni-font-size-lg;
    font-weight: bold;
    color: $uni-text-color;
    margin-bottom: 20rpx;
    line-height: 1.4;
  }

  .description {
    font-size: $uni-font-size-base;
    color: $uni-text-color-grey;
    line-height: 1.6;
  }
}

.rich-content {
  border-top: 1rpx solid $uni-border-color;
  padding: 30rpx;

  .section-title {
    font-size: $uni-font-size-lg;
    font-weight: 600;
    color: $uni-text-color;
    margin-bottom: 20rpx;
  }

  .rich-text-content {
    font-size: $uni-font-size-base;
    line-height: 1.8;
    color: $uni-text-color;

    // 富文本内容样式
    :deep(p) {
      margin-bottom: 16rpx;
    }

    :deep(img) {
      max-width: 100%;
      height: auto;
      border-radius: $uni-border-radius-base;
      margin: 16rpx 0;
    }

    :deep(h1), :deep(h2), :deep(h3) {
      font-weight: bold;
      margin: 24rpx 0 16rpx;
    }

    :deep(ul), :deep(ol) {
      padding-left: 40rpx;
      margin-bottom: 16rpx;
    }
  }
}

.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: $uni-bg-color;
  padding: 20rpx 30rpx;
  display: flex;
  gap: 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);

  .vote-btn, .rank-btn {
    flex: 1;
    height: 80rpx;
    border-radius: $uni-border-radius-base;
    font-size: $uni-font-size-base;
    font-weight: 500;
    border: none;
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
    }
  }

  .vote-btn {
    background-color: $uni-color-primary;
    color: $uni-text-color-inverse;

    &.btn-disabled {
      background-color: rgba($uni-text-color-disable, 0.5);
    }

    &:active:not(.btn-disabled) {
      background-color: rgba($uni-color-primary, 0.8);
    }
  }

  .rank-btn {
    background-color: rgba($uni-color-primary, 0.1);
    color: $uni-color-primary;
    border: 1rpx solid rgba($uni-color-primary, 0.3);

    &:active {
      background-color: rgba($uni-color-primary, 0.2);
    }
  }
}
</style>
