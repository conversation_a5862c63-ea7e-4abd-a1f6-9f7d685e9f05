<template>
  <view class="vote-list">
    <view
      v-for="item in voteList"
      :key="item.id"
      class="vote-item"
      @click="goToVote(item.id)"
    >
      <image :src="item.images?.[0]" class="vote-image" mode="aspectFill" />
      <view class="vote-title">{{ item.title }}</view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import http from '@/utils/request';

const voteList = ref<Array<{ id: string; title: string; images: string[] }>>([]);

const goToVote = (id: string) => {
  uni.navigateTo({
    url: `/pages/index/index?id=${id}`
  });
};

onMounted(async () => {
  // 通过API获取投票活动列表
  try {
    const res = await http.get('/api.app/index') as any;
    // 假设返回数据结构为 { code: 0, data: [...] }
    if (res && res.code === 1 && Array.isArray(res.data)) {
      voteList.value = res.data;
    } else {
      voteList.value = [];
    }
  } catch (e) {
    voteList.value = [];
  }
});
</script>

<style scoped lang="scss">
.vote-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding: 20rpx;
}
.vote-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
  padding: 24rpx 0;
  cursor: pointer;
}
.vote-image {
  width: 90vw;
  height: 240rpx;
  border-radius: 12rpx;
  object-fit: cover;
}
.vote-title {
  margin-top: 16rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #222;
}
</style> 