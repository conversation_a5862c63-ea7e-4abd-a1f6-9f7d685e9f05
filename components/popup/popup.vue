<template>
  <view 
    class="popup-mask" 
    :class="{ 'popup-mask-show': visible, 'popup-mask-animate': animateShow }" 
    @click="handleMaskClick"
    @touchmove.stop.prevent
  >
    <view class="popup-content" :class="[{ 'popup-content-animate': animateShow }, position]" @click.stop>
      <view class="popup-header" v-if="showHeader">
        <text class="popup-title">{{ title }}</text>
        <text class="popup-close" @click="handleClose">×</text>
      </view>
      <view class="popup-body">
        <slot></slot>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, onUnmounted } from 'vue'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  position: {
    type: String,
    default: 'center',
    validator: (value: string) => ['center', 'bottom', 'top'].includes(value)
  },
  title: {
    type: String,
    default: ''
  },
  showHeader: {
    type: <PERSON>olean,
    default: true
  },
  maskClosable: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:show', 'close'])

const animateShow = ref(false)
const visible = ref(false)

// 控制body滚动
const disableBodyScroll = () => {
  // #ifdef WEB
  document.body.style.overflow = 'hidden'
  // #endif
}

const enableBodyScroll = () => {
  // #ifdef WEB
  document.body.style.overflow = ''
  // #endif
}

watch(() => props.show, (val) => {
  if (val) {
    visible.value = true
    disableBodyScroll()
    nextTick(() => {
      animateShow.value = true
    })
  } else {
    animateShow.value = false
    setTimeout(() => {
      visible.value = false
      enableBodyScroll()
    }, 300)
  }
}, { immediate: true })

// 组件卸载时确保恢复滚动
onUnmounted(() => {
  enableBodyScroll()
})

const handleClose = () => {
  emit('update:show', false)
  emit('close')
}

const handleMaskClick = () => {
  if (props.maskClosable) {
    handleClose()
  }
}
</script>

<style lang="scss">
.popup-mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0);
  z-index: 999;
  @include flex-center;
  visibility: hidden;
  transition: background-color 0.3s cubic-bezier(.55, 0, .1, 1);
  
  // 微信小程序环境下，通过触摸事件阻止滚动
  // #ifdef MP-WEIXIN
  touch-action: none;
  // #endif

  &.popup-mask-show {
    visibility: visible;
    // 弹窗显示时阻止底层滚动
    // #ifdef MP-WEIXIN
    touch-action: none;
    // #endif
  }

  &.popup-mask-animate {
    background-color: $uni-bg-color-mask;
  }

  .popup-content {
    background-color: $uni-bg-color;
    border-radius: $uni-border-radius-lg;
    width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    opacity: 0;
    transform: scale(0.95) translateY(30rpx);
    transition: all 0.3s cubic-bezier(.55, 0, .1, 1);
    
    // 微信小程序环境下，允许弹窗内容滚动
    // #ifdef MP-WEIXIN
    touch-action: pan-y;
    // #endif

    &.center {
      transform: translateY(30rpx);
    }

    &.bottom {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      width: 100%;
      border-radius: $uni-border-radius-lg $uni-border-radius-lg 0 0;
      transform: translateY(100%);
    }

    &.top {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      width: 100%;
      border-radius: 0 0 $uni-border-radius-lg $uni-border-radius-lg;
      transform: translateY(-100%);
    }

    &.popup-content-animate {
      opacity: 1;
      transform: scale(1) translateY(0);
    }

    .popup-header {
      @include flex-between;
      padding: $uni-spacing-row-base;
      border-bottom: 1rpx solid $uni-border-color;

      .popup-title {
        font-size: $uni-font-size-lg;
        font-weight: bold;
      }

      .popup-close {
        font-size: 40rpx;
        color: $uni-text-color-grey;
        padding: 0 $uni-spacing-row-base;
      }
    }

    .popup-body {
      padding: $uni-spacing-row-base;
    }
  }
}
</style>