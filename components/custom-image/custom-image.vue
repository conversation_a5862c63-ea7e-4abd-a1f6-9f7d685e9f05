<template>
    <view class="custom-image">
        <image :src="src" :mode="mode" :lazy-load="lazyLoad" class="custom-image__img" @click="handlePreview"
            @load="handleImageLoad" @error="handleImageError" />

        <!-- 大图预览弹窗 -->
        <custom-popup v-model:show="previewShow" :show-header="false" :mask-closable="true" @close="handleClose"
            position="center">
            <view class="preview-container" :style="{ height: containerHeight }">
                <image :src="previewSrc || src" mode="aspectFit" class="preview-image" @click.stop
                    @load="handleImageLoad" @error="handleImageError" />
                <view class="preview-info" v-if="showInfo">
                    <text class="preview-title">{{ title }}</text>
                    <text class="preview-desc" v-if="description">{{ description }}</text>
                </view>
            </view>
        </custom-popup>
    </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import CustomPopup from '@/components/popup/popup.vue'

const props = defineProps({
    src: {
        type: String,
        required: true
    },
    mode: {
        type: String,
        default: 'aspectFill'
    },
    lazyLoad: {
        type: Boolean,
        default: true
    },
    // 预览时的图片源，如果不传则使用原图
    previewSrc: {
        type: String,
        default: ''
    },
    // 是否显示预览信息
    showInfo: {
        type: Boolean,
        default: false
    },
    // 预览标题
    title: {
        type: String,
        default: ''
    },
    // 预览描述
    description: {
        type: String,
        default: ''
    }
})

const previewShow = ref(false)
const imageWidth = ref(0)
const imageHeight = ref(0)
const containerHeight = ref('auto')

const handlePreview = () => {
    previewShow.value = true
}

const handleClose = () => {
    previewShow.value = false
}

const handleImageLoad = (e: any) => {
    // 获取图片实际尺寸
    const { width, height } = e.detail
    imageWidth.value = width
    imageHeight.value = height

    // 计算合适的容器高度
    const maxHeight = uni.getSystemInfoSync().windowHeight * 0.8 // 80vh
    const maxWidth = uni.getSystemInfoSync().windowWidth * 0.9 // 90vw

    let targetHeight = height
    let targetWidth = width

    // 如果图片太大，按比例缩小
    if (width > maxWidth) {
        const ratio = maxWidth / width
        targetWidth = maxWidth
        targetHeight = height * ratio
    }

    if (targetHeight > maxHeight) {
        const ratio = maxHeight / targetHeight
        targetHeight = maxHeight
        targetWidth = targetWidth * ratio
    }

    // 设置容器高度
    containerHeight.value = `${targetHeight}px`
}

const handleImageError = (e: any) => {
    // 图片加载失败时使用默认高度
    containerHeight.value = '400rpx'
}
</script>

<style lang="scss">
.custom-image {
    display: block;
    width: 100%;
    height: 100%;
    min-height: 200rpx;
    position: relative;

    &__img {
        display: block;
        width: 100%;
        height: 100%;
        min-height: inherit;
        /* 微信小程序特定样式 */
        /* #ifdef MP-WEIXIN */
        vertical-align: top;
        /* #endif */
    }
}

.preview-container {
    position: relative;
    width: 100%;
    height: auto;
    min-height: 200rpx;
    max-height: 80vh;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    transition: height 0.3s ease;

    .preview-image {
        width: 100%;
        height: 100%;
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }

    .preview-info {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
        padding: $uni-spacing-row-base;
        color: $uni-text-color-inverse;

        .preview-title {
            display: block;
            font-size: $uni-font-size-lg;
            font-weight: bold;
            margin-bottom: $uni-spacing-col-sm;
        }

        .preview-desc {
            display: block;
            font-size: $uni-font-size-base;
            opacity: 0.9;
        }
    }
}
</style>