<template>
  <view class="list">
    <view class="list-item" v-for="(item, index) in list" :key="index" :style="listItemStyle">
      <view class="image_box" @click="goToDetail(item)">
        <view class="votes" v-if="item.votes !== undefined && item.votes !== null">
          {{ formatVotes(item.votes) }}
        </view>
        <image :src="item.image" mode="aspectFill" lazy-load class="item-image" />
      </view>
      <view class="main-title" @click="goToDetail(item)">{{ item.name }}</view>
      <view class="content" @click="goToDetail(item)">
        <view class="box">
          <view class="desc">{{ item.description }}</view>
        </view>
      </view>
      <view class="vote-btn">
        <button class="primary-btn" :class="{ 'btn-disabled': disable }" @click="vote(item.id)" :disabled="disable">投 票</button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import http from '@/utils/request'
const props = defineProps({
  id: {
    type: Number,
    default: 0
  }
})

interface VoteItem {
  id: number
  name: string
  description: string
  image: string
  votes: number
  content?: string
}

const list = ref<VoteItem[]>([])
const disable = ref(false)

// 根据环境变量计算布局列数
const layoutColumns = computed(() => {
  return import.meta.env.VITE_VOTE_LAYOUT_COLUMNS || 2
})

// 根据列数计算列表项宽度
const listItemStyle = computed(() => {
  const columns = layoutColumns.value
  if (columns === '1') {
    return { width: '100%' }
  } else {
    // 双列布局：50%宽度减去间距
    return { width: 'calc(50% - 20rpx)' }
  }
})

const formatVotes = (votes: number) => {
  if (votes >= 10000) {
    return `${(votes / 10000).toFixed(1)}w`
  } else if (votes >= 1000) {
    return `${(votes / 1000).toFixed(1)}k`
  }
  return votes.toString()
}

const goToDetail = (item: VoteItem) => {
  uni.navigateTo({
    url: `/pages/detail/detail?id=${item.id}&vote_id=${props.id}`
  })
}

const vote = async (id: number) => {
  if (disable.value) return
  disable.value = true
  const token = uni.getStorageSync('token')
  if (!token) {
    uni.showToast({ title: '请先登录', icon: 'none' })
    disable.value = false
    return
  }
  try {
    const res: any = await http.post('/api.vote/vote', { id, vote_id: props.id })
    if (res.code === 1) {
      const newList = list.value.map(item => item.id === res.data.id ? res.data : item)
      list.value = newList
      uni.showToast({ title: '投票成功', icon: 'success' })
    } else {
      uni.showToast({ title: res.msg || '投票失败', icon: 'none' })
    }
  } catch (error) {
    uni.showToast({ title: '系统繁忙，请重试', icon: 'none' })
  } finally {
    disable.value = false
  }
}

const getOptions = async () => {
  try {
    const res: any = await http.get('/api.vote/options', { vote_id: props.id })
    if (res.code === 1) {
      list.value = res.data.list
    }
  } catch (error) {
    console.error('获取选项失败:', error)
  }
}

onMounted(() => {
  getOptions()
})

defineExpose({ list })
</script>

<style lang="scss" scoped>
.list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 $uni-spacing-row-sm;

  .list-item {
    margin: $uni-spacing-row-sm;
    background: $uni-bg-color;
    border-radius: $uni-border-radius-base;
    overflow: hidden;
    @include box-shadow(1);

    .image_box {
      position: relative;
      overflow: hidden;
      height: 300rpx;

      .votes {
        position: absolute;
        top: $uni-spacing-col-sm;
        right: $uni-spacing-col-sm;
        background: rgba(255, 255, 255, 0.8);
        color: $uni-color-primary;
        padding: 2rpx 8rpx;
        border-radius: 20rpx;
        font-size: $uni-font-size-sm;
        font-weight: 500;
        min-width: 60rpx;
        text-align: center;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
        letter-spacing: 1rpx;
        z-index: 1;
      }

      .item-image {
        width: 100%;
        height: 100%;
      }
    }

    .main-title {
      font-size: $uni-font-size-base;
      font-weight: bold;
      padding: $uni-spacing-col-sm;
    }

    .content {
      padding: 0 $uni-spacing-col-sm;

      .desc {
        font-size: $uni-font-size-sm;
        color: $uni-text-color-grey;
        @include multi-ellipsis(2);
      }
    }

    .vote-btn {
      padding: $uni-spacing-row-sm;

      .primary-btn {
        width: 100%;
        background-color: rgba($uni-color-primary, 0.9);
        color: $uni-text-color-inverse;
        font-size: $uni-font-size-base;
        padding: 4rpx 0;
        border-radius: $uni-border-radius-base;
        transition: all 0.3s ease;
        border: none;

        &:active {
          background-color: rgba($uni-color-primary, 0.8);
          transform: scale(0.98);
        }

        &.btn-disabled {
          background-color: rgba($uni-text-color-disable, 0.5);
        }
      }
    }
  }
}
</style>
