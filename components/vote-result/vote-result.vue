<template>
  <custom-popup v-model:show="show" title="实时排名" @close="handleClose">
    <view class="results">
      <view class="results-header">
        <view class="total-info">
          <text class="total-text">共 {{ sortList.length }} 个选项</text>
          <text class="total-votes">总票数: {{ totalVotes }}</text>
        </view>
      </view>
      <scroll-view class="results-scroll" scroll-y>
        <view v-if="sortList.length === 0" class="empty-state">
          <text class="empty-text">暂无投票数据</text>
        </view>
        <template v-for="(item, index) in sortList" :key="index">
          <view class="item" :class="'item-' + (index + 1)">
            <view class="rank-info">
              <view class="rank-number">
                <text>{{ index + 1 }}</text>
              </view>
              <view class="item-name">{{ item.name }}</view>
              <view class="item-votes">{{ item.votes }}票</view>
            </view>
            <view class="bar-container">
              <view class="bar" :class="{ 'bar-mini': getBarWidth(item.votes) < 10 }">
                <view class="bar-progress" :style="{ width: getBarWidth(item.votes) + '%' }">
                </view>
              </view>
              <view class="percentage">{{ getPercentage(item.votes) }}%</view>
            </view>
          </view>
        </template>

      </scroll-view>
    </view>
  </custom-popup>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import CustomPopup from '@/components/popup/popup.vue'

// 定义接口类型
interface VoteItem {
  id: number
  name: string
  description: string
  image: string
  votes: number
  content?: string
}

// 定义props
interface Props {
  modelValue: boolean
  list: VoteItem[]
}

// 定义emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 计算属性
const show = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const sortList = computed(() => {
  return [...props.list].sort((a, b) => b.votes - a.votes)
})

const totalVotes = computed(() => {
  return sortList.value.reduce((total, item) => total + item.votes, 0)
})

// 方法
const handleClose = () => {
  show.value = false
}


const getBarWidth = (votes: number) => {
  if (sortList.value.length === 0 || sortList.value[0].votes === 0) return 0
  return Math.max((votes * 100 / sortList.value[0].votes), 2) // 最小宽度2%
}

const getPercentage = (votes: number) => {
  if (totalVotes.value === 0) return '0.00'
  return ((votes * 100) / totalVotes.value).toFixed(1)
}
</script>

<style lang="scss">
.results {
  .results-header {
    padding: $uni-spacing-row-base;
    background-color: $uni-bg-color;
    border-bottom: 1rpx solid $uni-border-color;

    .total-info {
      @include flex-between;

      .total-text {
        font-size: $uni-font-size-base;
        color: $uni-text-color-grey;
      }

      .total-votes {
        font-size: $uni-font-size-base;
        color: $uni-text-color-grey;
      }
    }
  }

  .results-scroll {
    max-height: 60vh;
    overflow-y: auto;

    .empty-state {
      @include flex-center;
      height: 200rpx;

      .empty-text {
        font-size: $uni-font-size-base;
        color: $uni-text-color-grey;
      }
    }

    .item {
      padding: $uni-spacing-row-base;
      border-bottom: 1rpx solid $uni-border-color;

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background-color: rgba($uni-color-primary, 0.02);
      }

      .bar-progress {
        background: $uni-color-primary;
      }

      .rank-number {
        background: #f5f5f5;
        color: #b0b0b0;
      }

      &.item-1 {
        .bar-progress, .rank-number {
          background: #a259ff;
          color: #fff;
        }
      }

      &.item-2 {
        .bar-progress, .rank-number {
          background: #ffd700;
          color: #fff;
        }
      }

      &.item-3 {
        .bar-progress, .rank-number {
          background: #34c759;
          color: #fff;
        }
      }

      .rank-info {
        display: flex;
        align-items: center;
        gap: 16rpx;
        margin-bottom: $uni-spacing-col-sm;

        .rank-number {
          width: 56rpx;
          height: 56rpx;
          @include flex-center;
          font-size: 32rpx;
          font-weight: bold;
          border-radius: 50%;
          margin-right: $uni-spacing-col-sm;
        }

        .item-name {
          flex: 1;
          font-size: $uni-font-size-base;
          font-weight: 500;
          color: $uni-text-color;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .item-votes {
          font-size: $uni-font-size-base;
          color: $uni-text-color-grey;
          font-weight: 500;
          margin-left: 12rpx;
          white-space: nowrap;
        }
      }

      .bar-container {
        @include flex-between;
        align-items: center;
        gap: $uni-spacing-col-sm;

        .bar {
          flex: 1;
          height: 24rpx;
          background: #e5e5e5;
          border-radius: 12rpx;
          overflow: hidden;
          position: relative;

          .bar-progress {
            height: 100%;
            border-radius: 16rpx;
            transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            min-width: 12rpx;
          }
        }

        .percentage {
          min-width: 80rpx;
          text-align: right;
          font-size: $uni-font-size-sm;
          color: $uni-text-color-grey;
          font-weight: 500;
        }
      }
    }
  }
}
</style>
