
// Flex 布局
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// 文本截断
@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin multi-ellipsis($line: 2) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: $line;
  -webkit-box-orient: vertical;
}

// 响应式
@mixin respond-to($breakpoint) {
  @if $breakpoint == 'phone' {
    @media (max-width: 767px) { @content; }
  } @else if $breakpoint == 'tablet' {
    @media (min-width: 768px) and (max-width: 1023px) { @content; }
  } @else if $breakpoint == 'desktop' {
    @media (min-width: 1024px) { @content; }
  }
}

// 清除浮动
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 渐变背景
@mixin gradient-bg($start-color, $end-color, $direction: to right) {
  background: linear-gradient($direction, $start-color, $end-color);
}

// 阴影
@mixin box-shadow($level: 1) {
  @if $level == 1 {
    box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  } @else if $level == 2 {
    box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  } @else if $level == 3 {
    box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
  }
} 